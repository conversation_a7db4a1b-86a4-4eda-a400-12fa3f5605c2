<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分页功能测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="app/static/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="app/static/vendor/datatables/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="app/static/vendor/datatables/css/responsive.bootstrap5.min.css" rel="stylesheet">
    <!-- 统一分页样式 -->
    <link rel="stylesheet" href="app/static/css/unified-pagination.css">
    
    <style>
        body { padding: 20px; }
        .test-section { margin-bottom: 40px; }
        .screen-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="screen-info" id="screenInfo">
        屏幕宽度: <span id="screenWidth"></span>px
    </div>
    
    <div class="container-fluid">
        <h1>分页功能测试</h1>
        <p class="text-muted">测试统一分页管理器在不同屏幕尺寸下的表现</p>
        
        <!-- 测试表格1：订单数据明细（5列） -->
        <div class="test-section">
            <h3>订单数据明细（5列表格）</h3>
            <p class="text-info small">
                <i class="bi bi-info-circle"></i>
                优化后的布局：分页控件独占一行，信息文本在下方独占一行，确保在窄容器中有足够的显示空间
            </p>
            <div class="row">
                <div class="col-md-6">
                    <div class="table-responsive">
                        <table class="table table-striped data-table" id="orderTable">
                            <thead>
                                <tr>
                                    <th></th>
                                    <th>月份</th>
                                    <th>电商订单</th>
                                    <th>租赁订单</th>
                                    <th>总订单数</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 生成测试数据 -->
                                <tr><td></td><td>2024-01</td><td>1,234</td><td>567</td><td>1,801</td></tr>
                                <tr><td></td><td>2024-02</td><td>1,456</td><td>678</td><td>2,134</td></tr>
                                <tr><td></td><td>2024-03</td><td>1,678</td><td>789</td><td>2,467</td></tr>
                                <tr><td></td><td>2024-04</td><td>1,890</td><td>890</td><td>2,780</td></tr>
                                <tr><td></td><td>2024-05</td><td>2,012</td><td>901</td><td>2,913</td></tr>
                                <tr><td></td><td>2024-06</td><td>2,234</td><td>1,012</td><td>3,246</td></tr>
                                <tr><td></td><td>2024-07</td><td>2,456</td><td>1,123</td><td>3,579</td></tr>
                                <tr><td></td><td>2024-08</td><td>2,678</td><td>1,234</td><td>3,912</td></tr>
                                <tr><td></td><td>2024-09</td><td>2,890</td><td>1,345</td><td>4,235</td></tr>
                                <tr><td></td><td>2024-10</td><td>3,012</td><td>1,456</td><td>4,468</td></tr>
                                <tr><td></td><td>2024-11</td><td>3,234</td><td>1,567</td><td>4,801</td></tr>
                                <tr><td></td><td>2024-12</td><td>3,456</td><td>1,678</td><td>5,134</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 测试表格2：逾期数据明细（7列） -->
        <div class="test-section">
            <h3>逾期数据明细（7列表格）</h3>
            <p class="text-info small">
                <i class="bi bi-info-circle"></i>
                相同的优化布局：分页按钮有更多空间显示，信息文本清晰可见且不占用分页空间
            </p>
            <div class="row">
                <div class="col-md-6">
                    <div class="table-responsive">
                        <table class="table table-striped data-table" id="overdueTable">
                            <thead>
                                <tr>
                                    <th></th>
                                    <th>月份</th>
                                    <th>电商逾期</th>
                                    <th>租赁逾期</th>
                                    <th>总逾期数</th>
                                    <th>逾期金额</th>
                                    <th>逾期率</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr><td></td><td>2024-01</td><td>45</td><td>23</td><td>68</td><td>¥123,456</td><td>3.8%</td></tr>
                                <tr><td></td><td>2024-02</td><td>52</td><td>28</td><td>80</td><td>¥145,678</td><td>3.7%</td></tr>
                                <tr><td></td><td>2024-03</td><td>48</td><td>31</td><td>79</td><td>¥167,890</td><td>3.2%</td></tr>
                                <tr><td></td><td>2024-04</td><td>56</td><td>34</td><td>90</td><td>¥189,012</td><td>3.2%</td></tr>
                                <tr><td></td><td>2024-05</td><td>61</td><td>37</td><td>98</td><td>¥201,234</td><td>3.4%</td></tr>
                                <tr><td></td><td>2024-06</td><td>58</td><td>40</td><td>98</td><td>¥223,456</td><td>3.0%</td></tr>
                                <tr><td></td><td>2024-07</td><td>64</td><td>43</td><td>107</td><td>¥245,678</td><td>3.0%</td></tr>
                                <tr><td></td><td>2024-08</td><td>67</td><td>46</td><td>113</td><td>¥267,890</td><td>2.9%</td></tr>
                                <tr><td></td><td>2024-09</td><td>71</td><td>49</td><td>120</td><td>¥289,012</td><td>2.8%</td></tr>
                                <tr><td></td><td>2024-10</td><td>74</td><td>52</td><td>126</td><td>¥301,234</td><td>2.8%</td></tr>
                                <tr><td></td><td>2024-11</td><td>78</td><td>55</td><td>133</td><td>¥323,456</td><td>2.8%</td></tr>
                                <tr><td></td><td>2024-12</td><td>82</td><td>58</td><td>140</td><td>¥345,678</td><td>2.7%</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 测试控制面板 -->
        <div class="test-section">
            <h3>测试控制面板</h3>
            <div class="row">
                <div class="col-md-12">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="simulateResize(375)">超小屏幕 (375px)</button>
                        <button type="button" class="btn btn-outline-primary" onclick="simulateResize(480)">小屏幕 (480px)</button>
                        <button type="button" class="btn btn-outline-primary" onclick="simulateResize(768)">平板 (768px)</button>
                        <button type="button" class="btn btn-outline-primary" onclick="simulateResize(1024)">桌面 (1024px)</button>
                        <button type="button" class="btn btn-outline-success" onclick="resetSize()">重置</button>
                    </div>
                    <p class="mt-2 text-muted">点击按钮模拟不同屏幕尺寸下的分页效果</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="app/static/vendor/jquery/jquery.min.js"></script>
    <script src="app/static/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="app/static/vendor/datatables/js/jquery.dataTables.min.js"></script>
    <script src="app/static/vendor/datatables/js/dataTables.bootstrap5.min.js"></script>
    <script src="app/static/vendor/datatables/js/dataTables.responsive.min.js"></script>
    <script src="app/static/vendor/datatables/js/responsive.bootstrap5.min.js"></script>
    
    <!-- 统一分页管理器 -->
    <script src="app/static/js/unified-pagination-manager.js"></script>
    
    <script>
        // 更新屏幕宽度显示
        function updateScreenInfo() {
            document.getElementById('screenWidth').textContent = window.innerWidth;
        }
        
        // 模拟屏幕尺寸变化
        function simulateResize(width) {
            // 这里只是演示，实际需要开发者工具来真正改变视口
            alert(`请使用浏览器开发者工具将视口宽度设置为 ${width}px 来测试响应式效果`);
        }
        
        // 重置尺寸
        function resetSize() {
            alert('请在开发者工具中重置视口尺寸');
        }
        
        // 初始化
        $(document).ready(function() {
            updateScreenInfo();
            window.addEventListener('resize', updateScreenInfo);
            
            // 初始化表格
            const isMobile = window.innerWidth <= 768;
            
            // 订单表格 - 使用窄容器配置
            $('#orderTable').DataTable(window.getSummaryPagePaginationConfig(isMobile, true));

            // 逾期表格 - 使用窄容器配置
            $('#overdueTable').DataTable(window.getSummaryPagePaginationConfig(isMobile, true));
            
            console.log('测试页面初始化完成');
        });
    </script>
</body>
</html>
