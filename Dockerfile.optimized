# ===== 多阶段构建 Dockerfile =====
# 此文件适用于网络连接良好的环境

# ---- Builder Stage ----
# 使用Node.js镜像构建前端资源
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 拷贝前端依赖文件
COPY package.json package-lock.json* ./

# 配置npm镜像源并安装依赖
RUN npm config set registry https://registry.npmmirror.com && \
    npm install

# 拷贝前端源码
COPY webpack.config.js ./
COPY app/static/src ./app/static/src
COPY app/static/vendor ./app/static/vendor

# 构建前端资源
RUN npm run build

# ---- Final Stage ----
# 使用Python镜像作为运行环境
FROM python:3.11-slim

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    libglib2.0-0 \
    libsm6 \
    libxrender1 \
    libxext6 \
    libffi-dev \
    libpng-dev \
    libtiff5-dev \
    libopenjp2-7-dev \
    fonts-dejavu-core \
    fonts-liberation \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 配置pip镜像源
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple/ && \
    pip config set install.trusted-host pypi.tuna.tsinghua.edu.cn

# 升级pip和安装基础工具
RUN pip install --upgrade pip setuptools wheel

# 先安装numpy，确保版本兼容性
RUN pip install --no-cache-dir numpy==1.25.2

# 拷贝Python依赖文件并安装
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# 从builder阶段拷贝构建好的前端资源
COPY --from=builder /app/app/static/dist ./app/static/dist

# 拷贝应用源码
COPY . .

# 创建必要的目录
RUN mkdir -p logs cache && \
    chmod 755 logs cache

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser && \
    chown -R appuser:appuser /app
USER appuser

# 暴露端口
EXPOSE 5000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/ || exit 1

# 启动命令
CMD ["gunicorn", "-c", "gunicorn_config.py", "run:app"]